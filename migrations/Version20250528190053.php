<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250528190053 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE TABLE user (id INT AUTO_INCREMENT NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE cycling_tournament ADD website_id INT UNSIGNED DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE cycling_tournament ADD CONSTRAINT FK_47D19FEE18F45C82 FOREIGN KEY (website_id) REFERENCES website (id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_47D19FEE18F45C82 ON cycling_tournament (website_id)
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            DROP TABLE user
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE cycling_tournament DROP FOREIGN KEY FK_47D19FEE18F45C82
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX IDX_47D19FEE18F45C82 ON cycling_tournament
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE cycling_tournament DROP website_id
        SQL);
    }
}
