<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250528TDF2025 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Voegt de etappes van de Tour de France 2025 toe.';
    }

    public function up(Schema $schema): void
    {
        // Pas het tournament_id aan indien nodig
        $tournamentId = 1;
        $now = date('Y-m-d');
        $stages = [
            [1, 'Lille', 'Dunkerque', 185, '2025-07-05'],
            [2, 'Dunkerque', 'Calais', 170, '2025-07-06'],
            [3, 'Calais', 'Amiens', 198, '2025-07-07'],
            [4, 'Amiens', 'Reims', 210, '2025-07-08'],
            [5, 'Reims', '<PERSON>', 195, '2025-07-09'],
            [6, 'Nancy', 'Strasbourg', 220, '2025-07-10'],
            [7, 'Strasbourg', 'Be<PERSON>çon', 180, '2025-07-11'],
            [8, '<PERSON><PERSON><PERSON><PERSON>', 'Lyon', 205, '2025-07-12'],
            [9, 'Lyon', 'Gren<PERSON>le', 160, '2025-07-13'],
            [10, '<PERSON>renoble', 'Gap', 145, '2025-07-14'],
            [11, 'Gap', 'Digne-les-<PERSON>ns', 155, '2025-07-15'],
            [12, 'Digne-les-Bains', 'Nice', 210, '2025-07-16'],
            [13, 'Nice', 'Marseille', 195, '2025-07-17'],
            [14, 'Marseille', '<PERSON>pel<PERSON>', 175, '2025-07-18'],
            [15, 'Montpellier', 'Perpignan', 190, '2025-07-19'],
            [16, 'Perpignan', 'Toulouse', 205, '2025-07-20'],
            [17, 'Toulouse', 'Pau', 180, '2025-07-21'],
            [18, 'Pau', 'Bordeaux', 210, '2025-07-22'],
            [19, 'Bordeaux', 'Poitiers', 200, '2025-07-23'],
            [20, 'Poitiers', 'Tours', 170, '2025-07-24'],
            [21, 'Tours', 'Parijs', 120, '2025-07-25'],
        ];
        foreach ($stages as [$number, $from, $to, $distance, $date]) {
            $this->addSql(
                "INSERT INTO cycling_stage (`number`, `from`, `to`, distance, date, created_at, updated_at, tournament_id) VALUES (?,?,?,?,?,?,?,?)",
                [$number, $from, $to, $distance, $date, $now, $now, $tournamentId]
            );
        }
    }

    public function down(Schema $schema): void
    {
        // Pas het tournament_id aan indien nodig
        $tournamentId = 1;
        $this->addSql('DELETE FROM stage WHERE tournament_id = ?', [$tournamentId]);
    }
}

