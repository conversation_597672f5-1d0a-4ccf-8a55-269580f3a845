<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250528193126 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE TABLE cycling_stage (id INT AUTO_INCREMENT NOT NULL, number INT NOT NULL, `from` VARCHAR(255) NOT NULL, `to` VARCHAR(255) NOT NULL, distance INT NOT NULL, date DATE NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL, tournament_id INT UNSIGNED DEFAULT NULL, INDEX IDX_857576FB33D1A3E7 (tournament_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE cycling_stage ADD CONSTRAINT FK_857576FB33D1A3E7 FOREIGN KEY (tournament_id) REFERENCES cycling_tournament (id) ON DELETE CASCADE
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE cycling_stage DROP FOREIGN KEY FK_857576FB33D1A3E7
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE cycling_stage
        SQL);
    }
}
