<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250530100325 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE TABLE cycling_cyclist (id INT AUTO_INCREMENT NOT NULL, first_name VARCHAR(255) NOT NULL, last_name VA<PERSON><PERSON><PERSON>(255) NOT NULL, country VARCHAR(255) NOT NULL, inactive_at DATETIME DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL, team_id INT UNSIGNED DEFAULT NULL, INDEX IDX_231646F0296CD8AE (team_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE cycling_cyclist ADD CONSTRAINT FK_231646F0296CD8AE FOREIGN KEY (team_id) REFERENCES cycling_team (id) ON DELETE CASCADE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE cycling_team ADD created_at DATETIME NOT NULL, ADD updated_at DATETIME DEFAULT NULL
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE cycling_cyclist DROP FOREIGN KEY FK_231646F0296CD8AE
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE cycling_cyclist
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE cycling_team DROP created_at, DROP updated_at
        SQL);
    }
}
