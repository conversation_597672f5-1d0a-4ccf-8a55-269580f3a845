<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250527184345 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE cycling_tournament CHANGE registration_starts_at registration_starts_at DATETIME DEFAULT NULL, CHANGE registration_ends_at registration_ends_at DATETIME DEFAULT NULL, CHANGE external_id external_id INT DEFAULT NULL
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE cycling_tournament CHANGE registration_starts_at registration_starts_at DATETIME NOT NULL, CHANGE registration_ends_at registration_ends_at DATETIME NOT NULL, CHANGE external_id external_id INT NOT NULL
        SQL);
    }
}
