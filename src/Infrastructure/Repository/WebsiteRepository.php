<?php

declare(strict_types=1);

namespace App\Infrastructure\Repository;

use App\Domain\Website\Repository\WebsiteRepository as WebsiteRepositoryInterface;
use App\Domain\Website\Website;
use Doctrine\ORM\EntityManagerInterface;

final readonly class WebsiteRepository implements WebsiteRepositoryInterface
{
    public function __construct(
        private EntityManagerInterface $entityManager,
    ) {
    }

    public function find(int $id): ?Website
    {
        return $this->entityManager->find(Website::class, $id);
    }

    public function save(Website $website): void
    {
        $this->entityManager->persist($website);
        $this->entityManager->flush();
    }
}
