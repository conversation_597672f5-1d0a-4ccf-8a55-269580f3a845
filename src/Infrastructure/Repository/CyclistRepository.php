<?php

declare(strict_types=1);

namespace App\Infrastructure\Repository;

use App\Domain\Cycling\Cyclist\Cyclist;
use App\Domain\Cycling\Cyclist\Repository\CyclistRepository as DomainCyclistRepository;
use Doctrine\ORM\EntityManagerInterface;

final readonly class CyclistRepository implements DomainCyclistRepository
{
    public function __construct(
        private EntityManagerInterface $entityManager,
    ) {
    }

    public function save(Cyclist $cyclist): void
    {
        $this->entityManager->persist($cyclist);
        $this->entityManager->flush();
    }

    public function find(int $id): ?Cyclist
    {
        return $this->entityManager->getRepository(Cyclist::class)->find($id);
    }
}
