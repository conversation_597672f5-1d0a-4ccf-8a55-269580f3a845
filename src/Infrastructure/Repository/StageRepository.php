<?php

declare(strict_types=1);

namespace App\Infrastructure\Repository;

use App\Domain\Cycling\Stage\Repository\StageRepository as StageRepositoryInterface;
use App\Domain\Cycling\Stage\Stage;
use Doctrine\ORM\EntityManagerInterface;

final readonly class StageRepository implements StageRepositoryInterface
{
    public function __construct(
        private EntityManagerInterface $entityManager,
    ) {
    }

    public function find(int $id): ?Stage
    {
        return $this->entityManager->getRepository(Stage::class)->find($id);
    }

    public function save(Stage $stage): void
    {
        $this->entityManager->persist($stage);
        $this->entityManager->flush();
    }
}
