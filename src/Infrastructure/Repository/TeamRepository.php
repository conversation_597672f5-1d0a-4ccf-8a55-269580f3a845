<?php

declare(strict_types=1);

namespace App\Infrastructure\Repository;

use App\Domain\Cycling\Team\Repository\TeamRepository as DomainTeamRepository;
use App\Domain\Cycling\Team\Team;
use Doctrine\ORM\EntityManagerInterface;

class TeamRepository implements DomainTeamRepository
{
    public function __construct(private EntityManagerInterface $em)
    {
    }

    public function save(Team $team): void
    {
        $this->em->persist($team);
        $this->em->flush();
    }

    public function find(int $id): ?Team
    {
        return $this->em->getRepository(Team::class)->find($id);
    }
}
