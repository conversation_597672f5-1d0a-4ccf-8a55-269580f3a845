<?php

declare(strict_types=1);

namespace App\Infrastructure\Repository;

use App\Domain\Cycling\Tournament\Repository\TournamentRepository as TournamentRepositoryInterface;
use App\Domain\Cycling\Tournament\Tournament;
use Doctrine\ORM\EntityManagerInterface;

final readonly class TournamentRepository implements TournamentRepositoryInterface
{
    public function __construct(
        private EntityManagerInterface $entityManager,
    ) {
    }

    public function find(int $id): ?Tournament
    {
        return $this->entityManager->find(Tournament::class, $id);
    }

    public function save(Tournament $tournament): void
    {
        $this->entityManager->persist($tournament);
        $this->entityManager->flush();
    }
}
