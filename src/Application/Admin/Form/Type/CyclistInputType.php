<?php

declare(strict_types=1);

namespace App\Application\Admin\Form\Type;

use App\Application\Admin\Request\Input\CyclistInput;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\DateTimeType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

final class CyclistInputType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('firstName', TextType::class, [
                'label' => 'global.label_first_name',
            ])
            ->add('lastName', TextType::class, [
                'label' => 'global.label_last_name',
            ])
            ->add('country', TextType::class, [
                'label' => 'global.label_country',
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => CyclistInput::class,
        ]);
    }
}
