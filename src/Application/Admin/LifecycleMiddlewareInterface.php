<?php

declare(strict_types=1);

namespace App\Application\Admin;

use Symfony\Component\Form\FormInterface;

/**
 * This interface can be implemented on a Admin class.
 * It will give control how your entity will be created and edited, and is specifically meant for:
 *  1. DTO middleware, which will be directly mapped to the entity after validation.
 *  2. DtO middleware, which will be mapped to a command and dispatched.
 *
 * @template TEntity of object
 * @template TData of object
 */
interface LifecycleMiddlewareInterface
{
    /**
     * Returns the class-name of the DTO class.
     *
     * @return class-string<TData>
     */
    public function lifecycleMiddlewareFormDataClass(): string;

    /**
     * Creates a form where `data_class` has the return value from method lifecycleMiddlewareFormDataClass().
     */
    public function lifecycleMiddlewareForm(): FormInterface;

    /**
     * Transforms the submitted (valid) data from the form to $creatable that can be handled by
     * lifecycleMiddlewareDispatchCreatable().
     *
     * @param TData $submittedData
     *
     * @return TEntity
     */
    public function lifecycleMiddlewareTransformToCreatable(object $submittedData): object;

    /**
     * Handles the creation (persisting, flushing, dispatching) of $creatable.
     * $creatable can be passed to the model-manager, or dispatched to message-bus.
     *
     * The return value must be the created entity, which will be used by the followup flow (redirect).
     *
     * @param TEntity $creatable
     *
     * @return TEntity
     */
    public function lifecycleMiddlewareDispatchCreatable(object $creatable): object;

    /**
     * Transforms the entity to an instance of lifecycleMiddlewareFormDataClass().
     * Will be called as initial data state when starting the 'edit' action.
     *
     * @param TEntity $entity
     *
     * @return TData
     */
    public function lifecycleMiddlewareTransformToFormDataClass(object $entity): object;

    /**
     * Transforms the submitted (valid) data from the form to $editable that can be handled by
     * lifecycleMiddlewareDispatchEditable().
     *
     * @param TData $submittedData
     *
     * @return TEntity
     */
    public function lifecycleMiddlewareTransformToEditable(object $submittedData): object;

    /**
     * Handles the edit (flushing, dispatching) of $editable.
     * $editable can be passed to the model-manager, or dispatched to message-bus.
     *
     * The return value must be the updated entity, which will be used by the followup flow (redirect).
     *
     * @param TEntity $editable
     *
     * @return TEntity
     */
    public function lifecycleMiddlewareDispatchEditable(object $editable): object;
}
