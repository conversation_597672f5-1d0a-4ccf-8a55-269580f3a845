<?php

declare(strict_types=1);

namespace App\Application\Admin;

use Sonata\AdminBundle\Translator\LabelTranslatorStrategyInterface;

class UnderscoreGlobalLabelStrategy implements LabelTranslatorStrategyInterface
{
    /**
     * @var array<string, string>
     */
    private array $map;

    /**
     * @param array<string, string>|null $map
     */
    public function __construct(?array $map = null)
    {
        $this->map = $map ?: [
            'list' => 'global',
            'form' => 'global',
            'filter' => 'global',
        ];
    }

    public function getLabel(string $label, string $context = '', string $type = ''): string
    {
        $label = str_replace('.', '_', $label);
        $label = (string) preg_replace('~(?<=\\w)([A-Z])~', '_$1', $label);

        $context = $this->map[$context] ?? $context;

        return \sprintf('%s.%s_%s', $context, $type, mb_strtolower($label));
    }
}
