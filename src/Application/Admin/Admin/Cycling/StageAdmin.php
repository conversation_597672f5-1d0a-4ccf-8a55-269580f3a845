<?php

declare(strict_types=1);

namespace App\Application\Admin\Admin\Cycling;

use App\Application\Admin\Controller\CRUDController;
use App\Application\Admin\LifecycleMiddlewareInterface;
use App\Application\Admin\LifecycleMiddlewareTrait;
use App\Application\Admin\MessageBusAwareTrait;
use App\Application\Admin\Request\Input\StageInput;
use App\Application\Admin\UnderscoreGlobalLabelStrategy;
use App\Domain\Cycling\Stage\Command\CreateStageCommand;
use App\Domain\Cycling\Stage\Command\UpdateStageCommand;
use App\Domain\Cycling\Stage\Stage;
use App\Domain\Cycling\Tournament\Tournament;
use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\DependencyInjection\Attribute\Autoconfigure;
use Symfony\Component\Form\Extension\Core\Type\DateType;

/**
 * @extends AbstractAdmin<Stage>
 *
 * @implements LifecycleMiddlewareInterface<Stage, StageInput>
 *
 * @method Stage getSubject()
 */
#[Autoconfigure(
    tags: [
        [
            'controller' => CRUDController::class,
            'label' => 'global.label_stage',
            'label_translator_strategy' => UnderscoreGlobalLabelStrategy::class,
            'manager_type' => 'orm',
            'model_class' => Stage::class,
            'name' => 'sonata.admin',
            'group' => 'admin.group.cycling',
            'translation_domain' => 'admin',
        ],
    ],
    calls: [
        ['setMessageBus', ['@Symfony\\Component\\Messenger\\MessageBusInterface']],
    ],
)]
final class StageAdmin extends AbstractAdmin implements LifecycleMiddlewareInterface
{
    /** @use LifecycleMiddlewareTrait<Stage, StageInput> */
    use LifecycleMiddlewareTrait;
    use MessageBusAwareTrait;

    protected function generateBaseRouteName(bool $isChildAdmin = false): string
    {
        return 'admin_stage';
    }

    protected function generateBaseRoutePattern(bool $isChildAdmin = false): string
    {
        return 'stage';
    }

    protected function configureListFields(ListMapper $list): void
    {
        $list
            ->addIdentifier('name')
            ->add('distance')
            ->add('date')
            ->add('tournament');
    }

    protected function configureFormFields(FormMapper $form): void
    {
        $isChild = $this->isChild();

        $form
            ->with('global.general', ['class' => 'col-md-6'])
            ->add('number')
            ->add('from')
            ->add('to')
            ->add('distance')
            ->add('date', DateType::class)
            ->add('tournament', EntityType::class, [
                'class' => Tournament::class,
                'disabled' => $isChild,
            ])
            ->end();

        if ($isChild) {
            $form->get('tournament')->setData($this->getParent()->getSubject());
        }
    }

    public function lifecycleMiddlewareDispatchCreatable(object $creatable): object
    {
        return $this->lifecycleMiddlewareDispatchMessage($creatable);
    }

    public function lifecycleMiddlewareTransformToCreatable(object $submittedData): object
    {
        // @phpstan-ignore-next-line
        return new CreateStageCommand(
            $submittedData->number,
            $submittedData->from,
            $submittedData->to,
            $submittedData->distance,
            $submittedData->date,
            $this->getTournamentId($submittedData),
        );
    }

    public function lifecycleMiddlewareTransformToFormDataClass(object $entity): object
    {
        return StageInput::createFromEntity($entity);
    }

    public function lifecycleMiddlewareDispatchEditable(object $editable): Stage
    {
        return $this->lifecycleMiddlewareDispatchMessage($editable);
    }

    public function lifecycleMiddlewareTransformToEditable(object $submittedData): object
    {
        $stage = $this->getSubject();

        // @phpstan-ignore-next-line
        return new UpdateStageCommand(
            $stage->getId(),
            $submittedData->number,
            $submittedData->from,
            $submittedData->to,
            $submittedData->distance,
            $submittedData->date,
            $this->getTournamentId($submittedData),
        );
    }

    public function lifecycleMiddlewareFormDataClass(): string
    {
        return StageInput::class;
    }

    private function getTournamentId(object $submittedData): ?int
    {
        if ($this->isChild()) {
            $tournament = $this->getParent()->getSubject();
            if ($tournament instanceof Tournament) {
                return $tournament->getId();
            }
        }

        return $submittedData->getTournament()?->getId() ?? null;
    }
}
