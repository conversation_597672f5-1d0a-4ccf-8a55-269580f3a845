<?php

declare(strict_types=1);

namespace App\Application\Admin\Admin;

use App\Application\Admin\Controller\CRUDController;
use App\Application\Admin\LifecycleMiddlewareInterface;
use App\Application\Admin\LifecycleMiddlewareTrait;
use App\Application\Admin\MessageBusAwareTrait;
use App\Application\Admin\Request\Input\WebsiteInput;
use App\Application\Admin\UnderscoreGlobalLabelStrategy;
use App\Domain\Website\Command\CreateWebsiteCommand;
use App\Domain\Website\Command\UpdateWebsiteCommand;
use App\Domain\Website\Website;
use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Symfony\Component\DependencyInjection\Attribute\Autoconfigure;

/**
 * @extends AbstractAdmin<Website>
 *
 * @implements LifecycleMiddlewareInterface<Website, WebsiteInput>
 *
 * @method Website getSubject()
 */
#[Autoconfigure(
    tags: [
        [
            'controller' => CRUDController::class,
            'label' => 'global.label_website',
            'label_translator_strategy' => UnderscoreGlobalLabelStrategy::class,
            'manager_type' => 'orm',
            'model_class' => Website::class,
            'name' => 'sonata.admin',
            'group' => 'admin.group.website',
            'translation_domain' => 'admin',
        ],
    ],
    calls: [
        ['setMessageBus', ['@Symfony\\Component\\Messenger\\MessageBusInterface']],
    ],
)]
final class WebsiteAdmin extends AbstractAdmin implements LifecycleMiddlewareInterface
{
    /** @use LifecycleMiddlewareTrait<Website, WebsiteInput> */
    use LifecycleMiddlewareTrait;
    use MessageBusAwareTrait;

    protected function generateBaseRouteName(bool $isChildAdmin = false): string
    {
        return 'admin_website';
    }

    protected function generateBaseRoutePattern(bool $isChildAdmin = false): string
    {
        return 'website';
    }

    protected function configureListFields(ListMapper $list): void
    {
        $list
            ->addIdentifier('name')
            ->add('url')
            ->add('identifier');
    }

    protected function configureFormFields(FormMapper $form): void
    {
        $isEdit = $this->hasSubject();

        $form
            ->with('global.general', ['class' => 'col-md-6'])
                ->add('name')
                ->add('identifier', null, [
                    'disabled' => $isEdit,
                ])
                ->add('url')
            ->end();
    }

    public function lifecycleMiddlewareDispatchCreatable(object $creatable): object
    {
        return $this->lifecycleMiddlewareDispatchMessage($creatable);
    }

    public function lifecycleMiddlewareTransformToCreatable(object $submittedData): object
    {
        // @phpstan-ignore-next-line
        return new CreateWebsiteCommand(
            $submittedData->name,
            $submittedData->identifier,
            $submittedData->url
        );
    }

    public function lifecycleMiddlewareTransformToFormDataClass(object $entity): object
    {
        return WebsiteInput::createFromEntity($entity);
    }

    public function lifecycleMiddlewareFormDataClass(): string
    {
        return WebsiteInput::class;
    }

    public function lifecycleMiddlewareTransformToEditable(object $submittedData): object
    {
        $website = $this->getSubject();

        // @phpstan-ignore-next-line
        return new UpdateWebsiteCommand(
            $website->getId(),
            $submittedData->name,
            $submittedData->identifier,
            $submittedData->url
        );
    }

    public function lifecycleMiddlewareDispatchEditable(object $editable): Website
    {
        return $this->lifecycleMiddlewareDispatchMessage($editable);
    }
}
