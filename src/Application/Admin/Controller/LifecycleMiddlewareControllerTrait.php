<?php

declare(strict_types=1);

namespace App\Application\Admin\Controller;

use App\Application\Admin\LifecycleMiddlewareInterface;
use Exception;
use Sonata\AdminBundle\Admin\AdminInterface;
use Sonata\AdminBundle\Exception\LockException;
use Sonata\AdminBundle\Exception\ModelManagerException;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Throwable;

/**
 * This is an alternative solution to jarjobs/sonatadtomodelmanager (https://github.com/freshheads/FHAdminBundle/blob/5.x/Resources/doc/usage/dtos.md),
 * which will not work on SonataAdmin 4.x due to the closed API and new exception in Sonata\AdminBundle\Admin\AbstractAdmin::setSubject().
 *
 * @template TData of object
 * @template TEntity of object
 *
 * @property AdminInterface<TEntity>&LifecycleMiddlewareInterface<TEntity, TData> $admin
 */
trait LifecycleMiddlewareControllerTrait
{
    /**
     * These methods partially replaces Sonata\AdminBundle\Controller\CRUDController::createAction().
     *
     * The method contains a lot of lines of code. The changes compared with the original are:
     *  - [1. lifecycleMiddlewareForm] $form differs, it writes to a DTO instead of directly to the entity
     *  - [1a] formData is set when $object is an instance of form-data-class.
     *  - [2. lifecycleMiddlewareTransformToCreatable] $submittedObject is transformed from the $submittedData, instead of directly pulled from the form data
     *  - [3. Only for entities] Extra check for setSubject() and checkAccess(), which will not work in command setup
     *  - [4. lifecycleMiddlewareDispatchCreatable] $newObject is handled by the admin class, opening possibilities for command dispatches.
     *  - [5. lifecycleMiddlewareControllerExceptionHandler] Hook to catch exceptions and return own flash messages
     *
     * Everything else is copied from preCreate().
     *
     * @param TEntity $object
     *
     * @throws Exception|Throwable
     */
    protected function lifecycleMiddlewareControllerPreCreate(Request $request, object $object): Response
    {
        // @phpstan-ignore-next-line
        if (!$this->admin instanceof LifecycleMiddlewareInterface || !$this->admin instanceof AdminInterface) {
            throw new Exception('Excepting admin to implement LifecycleMiddlewareInterface on this point');
        }

        $templateKey = 'edit';

        // [1] Change with original
        $form = $this->admin->lifecycleMiddlewareForm();

        // [1a] Change with original
        if ($object::class === $this->admin->lifecycleMiddlewareFormDataClass()) {
            $form->setData($object);
        }

        $form->handleRequest($request);

        if ($form->isSubmitted()) {
            $isFormValid = $form->isValid();

            // persist if the form was valid and if in preview mode the preview was approved
            if ($isFormValid && (!$this->isInPreviewMode($request) || $this->isPreviewApproved($request))) {
                /** @var TData $submittedData */
                $submittedData = $form->getData();

                // [2] Change with original
                $submittedObject = $this->admin->lifecycleMiddlewareTransformToCreatable($submittedData);

                // [3] Change with original
                if ($submittedObject::class === $this->admin->getClass()) {
                    $this->admin->setSubject($submittedObject);
                    $this->admin->checkAccess('create', $submittedObject);
                }

                try {
                    // [4] Change with original
                    $newObject = $this->admin->lifecycleMiddlewareDispatchCreatable($submittedObject);

                    foreach ($this->admin->getExtensions() as $extension) {
                        $extension->postPersist($this->admin, $newObject);
                    }

                    if ($this->isXmlHttpRequest($request)) {
                        return $this->handleXmlHttpRequestSuccessResponse($request, $newObject);
                    }

                    $this->addFlash(
                        'sonata_flash_success',
                        $this->trans(
                            'flash_create_success',
                            ['%name%' => $this->escapeHtml($this->admin->toString($newObject))],
                            'SonataAdminBundle'
                        )
                    );

                    // redirect to edit mode
                    return $this->redirectTo($request, $newObject);
                } catch (ModelManagerException $e) {
                    $this->handleModelManagerException($e);

                    $isFormValid = false;
                } catch (Exception $e) {
                    // [5] Change with original
                    $this->lifecycleMiddlewareControllerExceptionHandler($e);
                }
            }

            // show an error message if the form failed validation
            if (!$isFormValid) {
                if ($this->isXmlHttpRequest($request) && null !== ($response = $this->handleXmlHttpRequestErrorResponse($request, $form))) {
                    return $response;
                }

                $this->addFlash(
                    'sonata_flash_error',
                    $this->trans(
                        'flash_create_error',
                        ['%name%' => $this->escapeHtml($this->admin->toString($object))],
                        'SonataAdminBundle'
                    )
                );
            } elseif ($this->isPreviewRequested($request)) {
                // pick the preview template if the form was valid and preview was requested
                $templateKey = 'preview';
                $this->admin->getShow();
            }
        }

        $formView = $form->createView();
        // set the theme for the current Admin Form
        $this->setFormTheme($formView, $this->admin->getFormTheme());

        return $this->render(
            $this->admin->getTemplateRegistry()->getTemplate($templateKey),
            [
                'action' => 'create',
                'form' => $formView,
                'object' => null,
                'objectId' => null,
                'admin' => $this->admin,
                'base_template' => $this->getBaseTemplateForRender($request),
            ],
        );
    }

    /**
     * These methods partially replaces Sonata\AdminBundle\Controller\CRUDController::editAction().
     *
     * The method contains a lot of lines of code. The changes compared with the original are:
     *  - [1. lifecycleMiddlewareForm] $form differs, it writes to a DTO instead of directly to the entity
     *  - [2. lifecycleMiddlewareTransformToFormDataClass] $form->setData accepts the DTO object, which should be reversed-transformed from the entity.
     *  - [3. lifecycleMiddlewareTransformToEditable] $submittedObject is transformed from the $submittedData, instead of directly pulled from the form data
     *  - [4. Only for entities] Extra check for setSubject() and checkAccess(), which will not work in command setup
     *  - [5. lifecycleMiddlewareDispatchEditable] $submittedObject is handled by the admin class, opening possibilities for command dispatches.
     *  - [6. lifecycleMiddlewareControllerExceptionHandler] Hook to catch exceptions and return own flash messages
     *
     * Everything else is copied from preCreate().
     *
     * @param TEntity $existingObject
     *
     * @throws Exception|Throwable
     */
    protected function lifecycleMiddlewareControllerPreEdit(Request $request, object $existingObject): Response
    {
        // @phpstan-ignore-next-line
        if (!$this->admin instanceof LifecycleMiddlewareInterface || !$this->admin instanceof AdminInterface) {
            throw new Exception('Excepting admin to implement LifecycleMiddlewareInterface on this point');
        }

        $templateKey = 'edit';

        $this->admin->setSubject($existingObject);
        $objectId = $this->admin->getNormalizedIdentifier($existingObject);

        // [1] Change with original
        $form = $this->admin->lifecycleMiddlewareForm();

        // [2] Change with original
        $transformedData = $this->admin->lifecycleMiddlewareTransformToFormDataClass($existingObject);

        $form->setData($transformedData);
        $form->handleRequest($request);

        if ($form->isSubmitted()) {
            $isFormValid = $form->isValid();

            // persist if the form was valid and if in preview mode the preview was approved
            if ($isFormValid && (!$this->isInPreviewMode($request) || $this->isPreviewApproved($request))) {
                /** @var TData $submittedData */
                $submittedData = $form->getData();

                // [3] Change with original
                $submittedObject = $this->admin->lifecycleMiddlewareTransformToEditable($submittedData);

                // [4] Change with original
                if ($submittedObject::class === $this->admin->getClass()) {
                    $this->admin->setSubject($submittedObject);
                }

                try {
                    // [5] Change with original
                    $existingObject = $this->admin->lifecycleMiddlewareDispatchEditable($submittedObject);

                    foreach ($this->admin->getExtensions() as $extension) {
                        $extension->postUpdate($this->admin, $existingObject);
                    }

                    if ($this->isXmlHttpRequest($request)) {
                        return $this->handleXmlHttpRequestSuccessResponse($request, $existingObject);
                    }

                    $this->addFlash(
                        'sonata_flash_success',
                        $this->trans(
                            'flash_edit_success',
                            ['%name%' => $this->escapeHtml($this->admin->toString($existingObject))],
                            'SonataAdminBundle'
                        )
                    );

                    // redirect to edit mode
                    return $this->redirectTo($request, $existingObject);
                } catch (ModelManagerException $e) {
                    $this->handleModelManagerException($e);

                    $isFormValid = false;
                } catch (LockException) {
                    $this->addFlash('sonata_flash_error', $this->trans('flash_lock_error', [
                        '%name%' => $this->escapeHtml($this->admin->toString($existingObject)),
                        '%link_start%' => \sprintf('<a href="%s">', $this->admin->generateObjectUrl('edit', $existingObject)),
                        '%link_end%' => '</a>',
                    ], 'SonataAdminBundle'));
                } catch (Exception $e) {
                    // [6] Change with original
                    $this->lifecycleMiddlewareControllerExceptionHandler($e);
                }
            }

            // show an error message if the form failed validation
            if (!$isFormValid) {
                if ($this->isXmlHttpRequest($request) && null !== ($response = $this->handleXmlHttpRequestErrorResponse($request, $form))) {
                    return $response;
                }

                $this->addFlash(
                    'sonata_flash_error',
                    $this->trans(
                        'flash_edit_error',
                        ['%name%' => $this->escapeHtml($this->admin->toString($existingObject))],
                        'SonataAdminBundle'
                    )
                );
            } elseif ($this->isPreviewRequested($request)) {
                // enable the preview template if the form was valid and preview was requested
                $templateKey = 'preview';
                $this->admin->getShow();
            }
        }

        $formView = $form->createView();
        // set the theme for the current Admin Form
        $this->setFormTheme($formView, $this->admin->getFormTheme());

        return $this->render(
            $this->admin->getTemplateRegistry()->getTemplate($templateKey),
            [
                'action' => 'edit',
                'form' => $formView,
                'object' => $existingObject,
                'objectId' => $objectId,
                'admin' => $this->admin,
                'base_template' => $this->getBaseTemplateForRender($request),
            ],
        );
    }

    /**
     * Can be overwritten in project, as a hook to handle custom exceptions.
     *
     * @throws Throwable
     */
    protected function lifecycleMiddlewareControllerExceptionHandler(Throwable $e): never
    {
        throw $e;
    }

    protected function getBaseTemplateForRender(Request $request): string
    {
        if ($this->isXmlHttpRequest($request)) {
            return $this->admin->getTemplateRegistry()->getTemplate('ajax');
        }

        return $this->admin->getTemplateRegistry()->getTemplate('layout');
    }
}
