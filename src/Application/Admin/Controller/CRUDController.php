<?php

declare(strict_types=1);

namespace App\Application\Admin\Controller;

use App\Application\Admin\LifecycleMiddlewareInterface;
use Sonata\AdminBundle\Controller\CRUDController as BaseCRUDController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\AsController;
use Throwable;

/**
 * @template TData of object
 * @template TEntity of object
 *
 * @extends BaseCRUDController<TEntity>
 */
#[AsController]
class CRUDController extends BaseCRUDController
{
    /** @use LifecycleMiddlewareControllerTrait<TData, TEntity> */
    use LifecycleMiddlewareControllerTrait;

    /**
     * @param TEntity $object
     *
     * @throws Throwable
     */
    protected function preCreate(Request $request, object $object): ?Response
    {
        if (!$this->admin instanceof LifecycleMiddlewareInterface) {
            return null;
        }

        return $this->lifecycleMiddlewareControllerPreCreate($request, $object);
    }

    /**
     * @param TEntity $object
     *
     * @throws Throwable
     */
    protected function preEdit(Request $request, object $object): ?Response
    {
        if (!$this->admin instanceof LifecycleMiddlewareInterface) {
            return null;
        }

        return $this->lifecycleMiddlewareControllerPreEdit($request, $object);
    }
}
