<?php

declare(strict_types=1);

namespace App\Application\Admin\Request\Input;

use App\Domain\Website\Website;
use Symfony\Component\Validator\Constraints as Assert;

final class WebsiteInput
{
    #[Assert\Length(max: 255)]
    #[Assert\NotBlank]
    public ?string $name = null;

    #[Assert\Length(max: 255)]
    #[Assert\NotBlank]
    public ?string $identifier = null;

    #[Assert\Length(max: 255)]
    #[Assert\NotBlank]
    #[Assert\Url]
    public ?string $url = null;

    public static function createFromEntity(Website $website): self
    {
        $input = new self();
        $input->name = $website->getName();
        $input->identifier = $website->getIdentifier();
        $input->url = $website->getUrl();

        return $input;
    }
}
