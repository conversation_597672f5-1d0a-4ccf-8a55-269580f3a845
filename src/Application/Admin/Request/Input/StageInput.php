<?php

declare(strict_types=1);

namespace App\Application\Admin\Request\Input;

use App\Domain\Cycling\Stage\Stage;
use App\Domain\Cycling\Tournament\Tournament;
use DateTimeImmutable;
use Symfony\Component\Validator\Constraints as Assert;

final class StageInput
{
    #[Assert\NotBlank]
    public ?int $number = null;

    #[Assert\NotBlank]
    #[Assert\Length(max: 255)]
    public ?string $from = null;

    #[Assert\NotBlank]
    #[Assert\Length(max: 255)]
    public ?string $to = null;

    #[Assert\NotBlank]
    public ?int $distance = null;

    #[Assert\NotBlank]
    public ?DateTimeImmutable $date = null;

    #[Assert\NotBlank]
    public ?Tournament $tournament = null;

    public static function createFromEntity(Stage $stage): self
    {
        $input = new self();
        $input->number = $stage->getNumber();
        $input->from = $stage->getFrom();
        $input->to = $stage->getTo();
        $input->distance = $stage->getDistance();
        $input->date = $stage->getDate();
        $input->tournament = $stage->getTournament();

        return $input;
    }
}
