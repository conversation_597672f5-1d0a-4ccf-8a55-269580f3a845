<?php

declare(strict_types=1);

namespace App\Application\Admin\Request\Input;

use App\Domain\Cycling\Tournament\Tournament;
use App\Domain\Website\Website;
use DateTimeImmutable;
use Symfony\Component\Validator\Constraints as Assert;

final class TournamentInput
{
    #[Assert\Length(max: 255)]
    #[Assert\NotBlank]
    public ?string $name = null;

    #[Assert\NotBlank]
    public ?Website $website = null;

    #[Assert\NotBlank]
    public ?DateTimeImmutable $startsAt = null;

    #[Assert\NotBlank]
    public ?DateTimeImmutable $endsAt = null;

    public ?DateTimeImmutable $registrationStartsAt = null;
    public ?DateTimeImmutable $registrationEndsAt = null;

    #[Assert\NotBlank]
    public ?int $year = null;

    public ?int $externalId = null;

    #[Assert\NotBlank]
    public ?int $nrOfCyclists = null;

    #[Assert\NotBlank]
    public ?int $nrOfReserves = null;

    public static function createFromEntity(Tournament $tournament): self
    {
        $input = new self();
        $input->name = $tournament->getName();
        $input->website = $tournament->getWebsite();
        $input->startsAt = $tournament->getStartsAt();
        $input->endsAt = $tournament->getEndsAt();
        $input->registrationStartsAt = $tournament->getRegistrationStartsAt();
        $input->registrationEndsAt = $tournament->getRegistrationEndsAt();
        $input->year = $tournament->getYear();
        $input->externalId = $tournament->getExternalId();
        $input->nrOfCyclists = $tournament->getNrOfCyclists();
        $input->nrOfReserves = $tournament->getNrOfReserves();

        return $input;
    }
}
