<?php

declare(strict_types=1);

namespace App\Application\Admin;

use InvalidArgumentException;
use Sonata\AdminBundle\Admin\AdminExtensionInterface;
use Sonata\AdminBundle\Builder\FormContractorInterface;
use Sonata\AdminBundle\Form\FormMapper;
use <PERSON>ymfony\Component\Form\FormInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Messenger\Stamp\HandledStamp;

/**
 * Partially implements FH\Bundle\AdminBundle\Admin\LifecycleMiddlewareInterface.
 *
 * Can be used from a object that implements:
 *  - Sonata\AdminBundle\Admin\AdminInterface
 *  - FH\Bundle\AdminBundle\Admin\LifecycleMiddlewareInterface
 *
 * @template TEntity of object
 * @template TData of object
 *
 * @property MessageBusInterface|null $messageBus
 */
trait LifecycleMiddlewareTrait
{
    /** @param FormMapper<TEntity> $form */
    abstract public function configureFormFields(FormMapper $form): void;

    /**
     * @param TEntity $object
     *
     * @return TEntity
     */
    abstract public function create(object $object): object;

    /** @return array<AdminExtensionInterface<TEntity>> */
    abstract public function getExtensions(): array;

    abstract public function getFormContractor(): FormContractorInterface;

    /** @return array<string, mixed> */
    abstract public function getFormOptions(): array;

    abstract public function getUniqid(): string;

    /** @return class-string<TData> */
    abstract public function lifecycleMiddlewareFormDataClass(): string;

    /**
     * @param TEntity $object
     *
     * @return TEntity
     */
    abstract public function update(object $object): object;

    public function lifecycleMiddlewareForm(): FormInterface
    {
        $formBuilder = $this->getFormContractor()->getFormBuilder(
            $this->getUniqid(),
            ['data_class' => $this->lifecycleMiddlewareFormDataClass()] + $this->getFormOptions(),
        );

        $mapper = new FormMapper($this->getFormContractor(), $formBuilder, $this);

        $this->configureFormFields($mapper);

        foreach ($this->getExtensions() as $extension) {
            $extension->configureFormFields($mapper);
        }

        return $formBuilder->getForm();
    }

    /**
     * Default implementation.
     * Overwrite when you want to dispatch a command.
     *
     * @param TEntity $creatable
     *
     * @return TEntity
     */
    public function lifecycleMiddlewareDispatchCreatable(object $creatable): object
    {
        return $this->create($creatable);
    }

    /**
     * Default implementation.
     * Overwrite when you want to dispatch a command.
     *
     * @param TEntity $editable
     *
     * @return TEntity
     */
    public function lifecycleMiddlewareDispatchEditable(object $editable): object
    {
        return $this->update($editable);
    }

    /**
     * Helper method that will dispatches a message to the message bus and returns the data.
     * Not part of the interface.
     */
    private function lifecycleMiddlewareDispatchMessage(object $message): object
    {
        if (!$this->messageBus instanceof MessageBusInterface) {
            throw new InvalidArgumentException('The message bus is not set.');
        }

        $envelope = $this->messageBus->dispatch($message);
        $handledStamp = $envelope->last(HandledStamp::class);

        if (!$handledStamp instanceof HandledStamp) {
            throw new InvalidArgumentException('Expected a HandledStamp');
        }

        return $handledStamp->getResult();
    }
}
