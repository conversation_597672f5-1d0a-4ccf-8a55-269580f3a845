<?php

declare(strict_types=1);

namespace App\Domain\Cycling\Cyclist\Command;

use App\Domain\Cycling\Cyclist\Cyclist;
use App\Domain\Cycling\Cyclist\Repository\CyclistRepository;
use App\Domain\Cycling\Cyclist\Resolver\CyclistResolver;
use App\Domain\Cycling\Team\Resolver\TeamResolver;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
final readonly class UpdateCyclistCommandHandler
{
    public function __construct(
        private CyclistRepository $cyclistRepository,
        private CyclistResolver $cyclistResolver,
        private TeamResolver $teamResolver,
    ) {
    }

    public function __invoke(UpdateCyclistCommand $command): Cyclist
    {
        $cyclist = $this->cyclistResolver->resolve($command->id);
        $team = $this->teamResolver->resolve($command->teamId);

        $cyclist->update(
            $command->firstName,
            $command->lastName,
            $command->country,
            $team,
            $command->inactiveAt
        );

        $this->cyclistRepository->save($cyclist);

        return $cyclist;
    }
}
