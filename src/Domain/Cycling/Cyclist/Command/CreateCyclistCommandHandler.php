<?php

declare(strict_types=1);

namespace App\Domain\Cycling\Cyclist\Command;

use App\Domain\Cycling\Cyclist\Cyclist;
use App\Domain\Cycling\Cyclist\Repository\CyclistRepository;
use App\Domain\Cycling\Team\Resolver\TeamResolver;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
final readonly class CreateCyclistCommandHandler
{
    public function __construct(
        private CyclistRepository $cyclistRepository,
        private TeamResolver $teamResolver,
    ) {
    }

    public function __invoke(CreateCyclistCommand $command): Cyclist
    {
        $team = $this->teamResolver->resolve($command->teamId);

        $cyclist = new Cyclist(
            $command->firstName,
            $command->lastName,
            $command->country,
            $team,
            $command->inactiveAt
        );

        $this->cyclistRepository->save($cyclist);

        return $cyclist;
    }
}
