<?php

declare(strict_types=1);

namespace App\Domain\Cycling\Cyclist\Resolver;

use App\Domain\Assert;
use App\Domain\Cycling\Cyclist\Cyclist;
use App\Domain\Cycling\Cyclist\Repository\CyclistRepository;

final readonly class CyclistResolver
{
    public function __construct(private CyclistRepository $repository)
    {
    }

    public function resolve(int $id): Cyclist
    {
        $cyclist = $this->repository->find($id);

        Assert::isInstanceOf($cyclist, Cyclist::class);

        return $cyclist;
    }
}
