<?php

declare(strict_types=1);

namespace App\Domain\Cycling\Team;

use App\Domain\Cycling\Cyclist\Cyclist;
use App\Domain\Cycling\Tournament\Tournament;
use App\Domain\TimestampableEntity;
use DateTimeImmutable;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
#[ORM\Table(name: 'cycling_team')]
class Team
{
    use TimestampableEntity;

    #[ORM\Id]
    #[ORM\Column(type: Types::INTEGER, options: ['unsigned' => true])]
    #[ORM\GeneratedValue]
    private ?int $id = null;

    #[ORM\Column(type: Types::STRING)]
    private string $name;

    #[ORM\Column(type: Types::STRING, length: 3)]
    private string $abbreviation;

    #[ORM\Column(type: Types::STRING)]
    private string $country;

    #[ORM\Column(type: Types::STRING, nullable: true)]
    private ?string $imageUrl;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    private ?int $externalId;

    #[ORM\ManyToOne(targetEntity: Tournament::class, inversedBy: 'teams')]
    #[ORM\JoinColumn(onDelete: 'CASCADE')]
    private ?Tournament $tournament;

    /**
     * @var Collection<int, Cyclist>
     */
    #[ORM\OneToMany(targetEntity: Cyclist::class, mappedBy: 'team')]
    private Collection $cyclists;

    public function __construct(
        string $name,
        string $abbreviation,
        string $country,
        ?Tournament $tournament = null,
        ?string $imageUrl = null,
        ?int $externalId = null,
    ) {
        $this->name = $name;
        $this->abbreviation = $abbreviation;
        $this->country = $country;
        $this->tournament = $tournament;
        $this->imageUrl = $imageUrl;
        $this->externalId = $externalId;
        $this->cyclists = new ArrayCollection();
        $this->createdAt = new DateTimeImmutable();
    }

    public function update(
        string $name,
        string $abbreviation,
        string $country,
        ?Tournament $tournament = null,
        ?string $imageUrl = null,
        ?int $externalId = null,
    ): void {
        $this->name = $name;
        $this->abbreviation = $abbreviation;
        $this->country = $country;
        $this->tournament = $tournament;
        $this->imageUrl = $imageUrl;
        $this->externalId = $externalId;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getAbbreviation(): string
    {
        return $this->abbreviation;
    }

    public function getCountry(): string
    {
        return $this->country;
    }

    public function getImageUrl(): ?string
    {
        return $this->imageUrl;
    }

    public function getExternalId(): ?int
    {
        return $this->externalId;
    }

    public function getTournament(): ?Tournament
    {
        return $this->tournament;
    }

    public function setTournament(?Tournament $tournament): void
    {
        $this->tournament = $tournament;
    }

    public function __toString(): string
    {
        return $this->name;
    }

    /**
     * @return Collection<int, Cyclist>
     */
    public function getCyclists(): Collection
    {
        return $this->cyclists;
    }
}
