<?php

declare(strict_types=1);

namespace App\Domain\Cycling\Team\Command;

use App\Domain\Cycling\Team\Repository\TeamRepository;
use App\Domain\Cycling\Team\Team;
use App\Domain\Cycling\Tournament\Resolver\TournamentResolver;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
final readonly class CreateTeamCommandHandler
{
    public function __construct(
        private TeamRepository $teamRepository,
        private TournamentResolver $tournamentResolver,
    ) {
    }

    public function __invoke(CreateTeamCommand $command): Team
    {
        $tournament = $this->tournamentResolver->resolve($command->tournamentId);

        $team = new Team(
            $command->name,
            $command->abbreviation,
            $command->country,
            $tournament,
            $command->imageUrl,
            $command->externalId
        );

        $this->teamRepository->save($team);

        return $team;
    }
}
