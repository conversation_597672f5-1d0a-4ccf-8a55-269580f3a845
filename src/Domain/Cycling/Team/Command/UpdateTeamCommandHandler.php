<?php

declare(strict_types=1);

namespace App\Domain\Cycling\Team\Command;

use App\Domain\Cycling\Team\Repository\TeamRepository;
use App\Domain\Cycling\Team\Resolver\TeamResolver;
use App\Domain\Cycling\Team\Team;
use App\Domain\Cycling\Tournament\Repository\TournamentRepository;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
final readonly class UpdateTeamCommandHandler
{
    public function __construct(
        private TeamRepository $teamRepository,
        private TournamentRepository $tournamentRepository,
        private TeamResolver $teamResolver,
    ) {
    }

    public function __invoke(UpdateTeamCommand $command): Team
    {
        $team = $this->teamResolver->resolve($command->id);
        $tournament = $this->tournamentRepository->find($command->tournamentId);

        $team->update(
            $command->name,
            $command->abbreviation,
            $command->country,
            $tournament,
            $command->imageUrl,
            $command->externalId
        );

        $this->teamRepository->save($team);

        return $team;
    }
}
