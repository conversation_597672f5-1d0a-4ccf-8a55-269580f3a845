<?php

declare(strict_types=1);

namespace App\Domain\Cycling\Team\Resolver;

use App\Domain\Assert;
use App\Domain\Cycling\Team\Repository\TeamRepository;
use App\Domain\Cycling\Team\Team;

final readonly class TeamResolver
{
    public function __construct(private TeamRepository $repository)
    {
    }

    public function resolve(int $id): Team
    {
        $team = $this->repository->find($id);

        Assert::isInstanceOf($team, Team::class);

        return $team;
    }
}
