<?php

declare(strict_types=1);

namespace App\Domain\Cycling\Tournament\Command;

use App\Domain\Cycling\Tournament\Repository\TournamentRepository;
use App\Domain\Cycling\Tournament\Tournament;
use App\Domain\Website\Resolver\WebsiteResolver;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
final readonly class CreateTournamentCommandHandler
{
    public function __construct(
        private TournamentRepository $repository,
        private WebsiteResolver $resolver,
    ) {
    }

    public function __invoke(CreateTournamentCommand $command): Tournament
    {
        $website = $this->resolver->resolve($command->websiteId);

        $tournament = new Tournament(
            $command->name,
            $command->startsAt,
            $command->endsAt,
            $command->registrationStartsAt,
            $command->registrationEndsAt,
            $command->year,
            $command->externalId,
            $command->nrOfCyclists,
            $command->nrOfReserves,
            $website
        );

        $this->repository->save($tournament);

        return $tournament;
    }
}
