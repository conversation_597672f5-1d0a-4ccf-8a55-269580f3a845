<?php

declare(strict_types=1);

namespace App\Domain\Cycling\Tournament\Command;

use App\Domain\Cycling\Tournament\Repository\TournamentRepository;
use App\Domain\Cycling\Tournament\Resolver\TournamentResolver;
use App\Domain\Cycling\Tournament\Tournament;
use App\Domain\Website\Resolver\WebsiteResolver;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
final readonly class UpdateTournamentCommandHandler
{
    public function __construct(
        private TournamentRepository $tournamentRepository,
        private TournamentResolver $tournamentResolver,
        private WebsiteResolver $websiteResolver,
    ) {
    }

    public function __invoke(UpdateTournamentCommand $command): Tournament
    {
        $tournament = $this->tournamentResolver->resolve($command->id);
        $website = $this->websiteResolver->resolve($command->websiteId);

        $tournament->update(
            $command->name,
            $command->startsAt,
            $command->endsAt,
            $command->registrationStartsAt,
            $command->registrationEndsAt,
            $command->year,
            $command->externalId,
            $command->nrOfCyclists,
            $command->nrOfReserves,
            $website,
        );

        $this->tournamentRepository->save($tournament);

        return $tournament;
    }
}
