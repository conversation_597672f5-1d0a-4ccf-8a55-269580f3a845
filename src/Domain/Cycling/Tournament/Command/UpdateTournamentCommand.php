<?php

declare(strict_types=1);

namespace App\Domain\Cycling\Tournament\Command;

use DateTimeImmutable;

final readonly class UpdateTournamentCommand
{
    public function __construct(
        public int $id,
        public string $name,
        public DateTimeImmutable $startsAt,
        public DateTimeImmutable $endsAt,
        public ?DateTimeImmutable $registrationStartsAt,
        public ?DateTimeImmutable $registrationEndsAt,
        public int $year,
        public ?int $externalId,
        public ?int $nrOfCyclists,
        public ?int $nrOfReserves,
        public int $websiteId,
    ) {
    }
}
