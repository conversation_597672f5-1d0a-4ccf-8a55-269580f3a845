<?php

declare(strict_types=1);

namespace App\Domain\Cycling\Tournament\Resolver;

use App\Domain\Assert;
use App\Domain\Cycling\Tournament\Repository\TournamentRepository;
use App\Domain\Cycling\Tournament\Tournament;

final readonly class TournamentResolver
{
    public function __construct(private TournamentRepository $repository)
    {
    }

    public function resolve(int $id): Tournament
    {
        $tournament = $this->repository->find($id);

        Assert::isInstanceOf($tournament, Tournament::class, 'tournament not found');

        return $tournament;
    }
}
