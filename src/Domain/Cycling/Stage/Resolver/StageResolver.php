<?php

declare(strict_types=1);

namespace App\Domain\Cycling\Stage\Resolver;

use App\Domain\Assert;
use App\Domain\Cycling\Stage\Repository\StageRepository;
use App\Domain\Cycling\Stage\Stage;

final readonly class StageResolver
{
    public function __construct(private StageRepository $repository)
    {
    }

    public function resolve(int $id): Stage
    {
        $stage = $this->repository->find($id);

        Assert::isInstanceOf($stage, Stage::class);

        return $stage;
    }
}
