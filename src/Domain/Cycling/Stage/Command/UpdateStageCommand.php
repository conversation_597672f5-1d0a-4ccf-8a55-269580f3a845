<?php

declare(strict_types=1);

namespace App\Domain\Cycling\Stage\Command;

use DateTimeImmutable;

final readonly class UpdateStageCommand
{
    public function __construct(
        public int $id,
        public int $number,
        public string $from,
        public string $to,
        public int $distance,
        public DateTimeImmutable $date,
        public int $tournamentId,
    ) {
    }
}
