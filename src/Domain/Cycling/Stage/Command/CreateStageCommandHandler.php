<?php

declare(strict_types=1);

namespace App\Domain\Cycling\Stage\Command;

use App\Domain\Cycling\Stage\Repository\StageRepository;
use App\Domain\Cycling\Stage\Stage;
use App\Domain\Cycling\Tournament\Resolver\TournamentResolver;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
final readonly class CreateStageCommandHandler
{
    public function __construct(
        private StageRepository $stageRepository,
        private TournamentResolver $tournamentResolver,
    ) {
    }

    public function __invoke(CreateStageCommand $command): Stage
    {
        $tournament = $this->tournamentResolver->resolve($command->tournamentId);

        $stage = new Stage(
            $command->number,
            $command->from,
            $command->to,
            $command->distance,
            $command->date,
            $tournament,
        );

        $this->stageRepository->save($stage);

        return $stage;
    }
}
