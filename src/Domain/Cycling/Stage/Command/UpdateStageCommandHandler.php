<?php

declare(strict_types=1);

namespace App\Domain\Cycling\Stage\Command;

use App\Domain\Cycling\Stage\Repository\StageRepository;
use App\Domain\Cycling\Stage\Resolver\StageResolver;
use App\Domain\Cycling\Stage\Stage;
use App\Domain\Cycling\Tournament\Resolver\TournamentResolver;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
final readonly class UpdateStageCommandHandler
{
    public function __construct(
        private StageRepository $stageRepository,
        private StageResolver $stageResolver,
        private TournamentResolver $tournamentResolver,
    ) {
    }

    public function __invoke(UpdateStageCommand $command): ?Stage
    {
        $stage = $this->stageResolver->resolve($command->id);
        $tournament = $this->tournamentResolver->resolve($command->tournamentId);

        $stage->update(
            $command->number,
            $command->from,
            $command->to,
            $command->distance,
            $command->date,
            $tournament,
        );

        $this->stageRepository->save($stage);

        return $stage;
    }
}
