<?php

declare(strict_types=1);

namespace App\Domain\Website\Resolver;

use App\Domain\Assert;
use App\Domain\Website\Repository\WebsiteRepository;
use App\Domain\Website\Website;

final readonly class WebsiteResolver
{
    public function __construct(private WebsiteRepository $repository)
    {
    }

    public function resolve(int $id): Website
    {
        $website = $this->repository->find($id);

        Assert::isInstanceOf($website, Website::class, 'website not found');

        return $website;
    }
}
