<?php

declare(strict_types=1);

namespace App\Domain\Website;

use App\Domain\Cycling\Tournament\Tournament;
use App\Domain\TimestampableEntity;
use DateTimeImmutable;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
#[ORM\Table(name: 'website')]
class Website
{
    use TimestampableEntity;

    #[ORM\Id]
    #[ORM\Column(type: Types::INTEGER, options: ['unsigned' => true])]
    #[ORM\GeneratedValue]
    private ?int $id = null;

    #[ORM\Column(type: Types::STRING)]
    private string $name;

    #[ORM\Column(type: Types::STRING, unique: true)]
    private string $identifier;

    #[ORM\Column(type: Types::STRING)]
    private string $url;

    /**
     * @var Collection<int, Tournament>
     */
    #[ORM\OneToMany(targetEntity: Tournament::class, mappedBy: 'websites')]
    private Collection $tournaments;

    public function __construct(string $name, string $identifier, string $url)
    {
        $this->name = $name;
        $this->identifier = $identifier;
        $this->url = $url;
        $this->createdAt = new DateTimeImmutable();
        $this->tournaments = new ArrayCollection();
    }

    public function update(string $name, string $identifier, string $url): void
    {
        $this->name = $name;
        $this->identifier = $identifier;
        $this->url = $url;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getIdentifier(): string
    {
        return $this->identifier;
    }

    public function getUrl(): string
    {
        return $this->url;
    }

    /**
     * @return Collection<int, Tournament>
     */
    public function getTournaments(): Collection
    {
        return $this->tournaments;
    }

    public function __toString(): string
    {
        return $this->name;
    }
}
