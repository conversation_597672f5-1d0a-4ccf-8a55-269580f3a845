<?php

declare(strict_types=1);

namespace App\Domain\Website\Command;

use App\Domain\Website\Repository\WebsiteRepository;
use App\Domain\Website\Website;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
final readonly class CreateWebsiteCommandHandler
{
    public function __construct(private WebsiteRepository $repository)
    {
    }

    public function __invoke(CreateWebsiteCommand $command): Website
    {
        $website = new Website(
            $command->name,
            $command->identifier,
            $command->url,
        );

        $this->repository->save($website);

        return $website;
    }
}
