<?php

declare(strict_types=1);

namespace App\Domain\Website\Command;

use App\Domain\Website\Repository\WebsiteRepository;
use App\Domain\Website\Resolver\WebsiteResolver;
use App\Domain\Website\Website;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
final readonly class UpdateWebsiteCommandHandler
{
    public function __construct(
        private WebsiteRepository $repository,
        private WebsiteResolver $resolver,
    ) {
    }

    public function __invoke(UpdateWebsiteCommand $command): Website
    {
        $website = $this->resolver->resolve($command->id);

        $website->update(
            $command->name,
            $command->identifier,
            $command->url,
        );

        $this->repository->save($website);

        return $website;
    }
}
