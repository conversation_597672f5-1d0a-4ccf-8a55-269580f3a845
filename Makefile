ifndef APP_ENV
	env?=dev
else
	env?=$(APP_ENV)
endif

.PHONY: install
install: install_vendors install_assets doctrine_migrate_database

# install vendors
.PHONY: install_vendors
install_vendors:
	symfony composer install

# install frontend assets
.PHONY: install_assets
install_assets:
	rm -rf public/bundles public/assets/*/build
	symfony php bin/console assets:install public --env=$(env) --symlink --relative

# install phpstan
.PHONY: install_phpstan
install_phpstan:
	composer update --working-dir=vendor-bin/phpstan

.PHONY: doctrine_migrate_database
doctrine_migrate_database:
	symfony console doc:mig:mig --no-interaction --env=$(env)

.PHONY: phpunit
phpunit:
	rm -rf var/cache/* && symfony php -d memory_limit=-1 bin/console cache:clear --env=test
	symfony php -d memory_limit=1024M vendor/bin/phpunit --configuration=phpunit.xml.dist --colors=always --stop-on-defect $(if $(FILTER),--filter=$(FILTER))

# analyze code
.PHONY: phpstan
phpstan: install_phpstan
	symfony php vendor-bin/phpstan/vendor/bin/phpstan --memory-limit=4G analyse

.PHONY: install-php-cs-fixer
install-php-cs-fixer:
	symfony composer bin php-cs-fixer update

.PHONY: php-cs-fixer-dry-run
php-cs-fixer-dry-run: install-php-cs-fixer
	symfony php vendor-bin/php-cs-fixer/vendor/friendsofphp/php-cs-fixer/php-cs-fixer fix --ansi --verbose --diff --dry-run

.PHONY: php-cs-fixer-fix
php-cs-fixer-fix: install-php-cs-fixer
	symfony php vendor-bin/php-cs-fixer/vendor/friendsofphp/php-cs-fixer/php-cs-fixer fix --ansi --verbose --diff

.PHONY: rector-dry-run
rector-dry-run:
	symfony php vendor/bin/rector process src --dry-run --config=rector.php

.PHONY: rector
rector:
	symfony php vendor/bin/rector process src --config=rector.php

.PHONY: composer-unused
composer-unused:
	symfony composer bin composer-unused update
	symfony php vendor-bin/composer-unused/vendor/icanhazstring/composer-unused/bin/composer-unused --configuration=composer-unused.php

.PHONY: lint-twig
lint-twig:
	symfony console lint:twig templates/

fix: php-cs-fixer-fix phpstan php-compatibility lint-twig

.PHONY: php-compatibility
php-compatibility:
	test -f .phpcs.xml.dist || (echo Configuration file does not exists, php-compatibility check aborted. Run:; echo $ cp vendor-bin/php-compatibility/.phpcs.xml.dist .; exit 1)
	symfony composer bin php-compatibility update
	symfony php vendor-bin/php-compatibility/vendor/squizlabs/php_codesniffer/bin/phpcs

.PHONY: start
start:
	docker compose up -d
	symfony proxy:start
	symfony proxy:domain:attach api.poolbeheer
	symfony proxy:domain:attach poolbeheer
	symfony server:start -d

.PHONY: stop
stop:
	docker compose down
	symfony server:stop
###< freshheads/platformsh-pack ###
