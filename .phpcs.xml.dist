<?xml version="1.0"?>

<ruleset name="PHP compatibility">
    <file>.</file>

    <exclude-pattern>/node_modules/*</exclude-pattern>
    <exclude-pattern>/var/*</exclude-pattern>
    <exclude-pattern>/vendor/*</exclude-pattern>
    <exclude-pattern>/vendor-bin/*</exclude-pattern>

    <!-- Only check PHP files. -->
    <arg name="extensions" value="php"/>

    <!-- Test for PHP version compatibility. Can be single version "8.2" or a range "8.1-8.2" -->
    <config name="testVersion" value="8.3"/>

    <!-- Run the PHP compatibility rule -->
    <rule ref="PHPCompatibility"/>

    <!-- Show progress, show the error codes for each message (source). -->
    <arg value="ps"/>

    <!-- Strip the filepaths down to the relevant bit. -->
    <arg name="basepath" value="./"/>
</ruleset>
