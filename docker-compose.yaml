name: 'poolbeheer'

services:
    database:
        image: "mariadb:10.11"
        command: mysqld --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci
        ports:
            - "40600:3306"
        environment:
            MYSQL_ALLOW_EMPTY_PASSWORD: true
            MYSQL_DATABASE: poolbeheer_dev
            TZ: UTC
        volumes:
            - "database:/var/lib/mysql"

volumes:
    database:
        driver: local
