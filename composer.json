{"type": "project", "license": "proprietary", "minimum-stability": "stable", "prefer-stable": true, "require": {"php": ">=8.3", "ext-ctype": "*", "ext-iconv": "*", "doctrine/doctrine-bundle": "^2.14", "doctrine/doctrine-migrations-bundle": "^3.4", "doctrine/orm": "^3.3", "gedmo/doctrine-extensions": "^3.20", "ramsey/uuid-doctrine": "^2.1", "sonata-project/admin-bundle": "^4.36", "sonata-project/doctrine-orm-admin-bundle": "^4.18", "symfony/console": "7.2.*", "symfony/dotenv": "7.2.*", "symfony/flex": "^2", "symfony/framework-bundle": "7.2.*", "symfony/messenger": "7.2.*", "symfony/runtime": "7.2.*", "symfony/yaml": "7.2.*", "webmozart/assert": "^1.11"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8", "phpunit/phpunit": "^11.0", "rector/rector": "^1.2.6", "symfony/debug-bundle": "7.2.*", "symfony/process": "7.2.*", "symfony/stopwatch": "7.2.*", "symfony/web-profiler-bundle": "7.2.*"}, "config": {"allow-plugins": {"php-http/discovery": true, "symfony/flex": true, "symfony/runtime": true, "bamarni/composer-bin-plugin": true}, "bump-after-update": true, "sort-packages": true}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php73": "*", "symfony/polyfill-php74": "*", "symfony/polyfill-php80": "*", "symfony/polyfill-php81": "*", "symfony/polyfill-php82": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "7.2.*"}}}