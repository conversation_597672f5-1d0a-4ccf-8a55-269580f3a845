{"doctrine/deprecations": {"version": "1.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "87424683adc81d7dc305eefec1fced883084aab9"}}, "doctrine/doctrine-bundle": {"version": "2.14", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.13", "ref": "620b57f496f2e599a6015a9fa222c2ee0a32adcb"}, "files": ["config/packages/doctrine.yaml", "src/Entity/.gitignore", "src/Repository/.gitignore"]}, "doctrine/doctrine-migrations-bundle": {"version": "3.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.1", "ref": "1d01ec03c6ecbd67c3375c5478c9a423ae5d6a33"}, "files": ["config/packages/doctrine_migrations.yaml", "migrations/.gitignore"]}, "knplabs/knp-menu-bundle": {"version": "v3.5.0"}, "phpstan/phpstan": {"version": "1.12", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.0", "ref": "5e490cc197fb6bb1ae22e5abbc531ddc633b6767"}, "files": ["phpstan.dist.neon"]}, "phpunit/phpunit": {"version": "11.5", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "10.0", "ref": "bb22cf8d8c554a623b427d5f3416b538f5525233"}, "files": [".env.test", "phpunit.dist.xml", "tests/bootstrap.php"]}, "ramsey/uuid-doctrine": {"version": "2.1", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.3", "ref": "471aed0fbf5620b8d7f92b7a5ebbbf6c0945c27a"}, "files": ["config/packages/ramsey_uuid_doctrine.yaml"]}, "sonata-project/admin-bundle": {"version": "4.36", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "4.0", "ref": "0e5931df1732e3dccfba42a20853049e5e9db6ae"}, "files": ["config/packages/sonata_admin.yaml", "config/routes/sonata_admin.yaml", "src/Admin/.gitignore"]}, "sonata-project/block-bundle": {"version": "5.2", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "4.11", "ref": "b4edd2a1e6ac1827202f336cac2771cb529de542"}, "files": ["config/packages/sonata_block.yaml"]}, "sonata-project/doctrine-extensions": {"version": "2.5", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.8", "ref": "4ea4a4b6730f83239608d7d4c849533645c70169"}}, "sonata-project/doctrine-orm-admin-bundle": {"version": "4.18.0"}, "sonata-project/exporter": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "2.4", "ref": "93d6df022ef1dc24bdfa8667ddd560bbde89a7cc"}}, "sonata-project/form-extensions": {"version": "2.5", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.4", "ref": "9c8a1e8ce2b1f215015ed16652c4ed18eb5867fd"}, "files": ["config/packages/sonata_form.yaml"]}, "sonata-project/twig-extensions": {"version": "2.5", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.2", "ref": "30dba2f9b719f21b497a6302f41aac07f9079e13"}}, "symfony/console": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "1781ff40d8a17d87cf53f8d4cf0c8346ed2bb461"}, "files": ["bin/console"]}, "symfony/debug-bundle": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "5aa8aa48234c8eb6dbdd7b3cd5d791485d2cec4b"}, "files": ["config/packages/debug.yaml"]}, "symfony/flex": {"version": "2.7", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.4", "ref": "52e9754527a15e2b79d9a610f98185a1fe46622a"}, "files": [".env", ".env.dev"]}, "symfony/form": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.2", "ref": "7d86a6723f4a623f59e2bf966b6aad2fc461d36b"}, "files": ["config/packages/csrf.yaml"]}, "symfony/framework-bundle": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.2", "ref": "87bcf6f7c55201f345d8895deda46d2adbdbaa89"}, "files": ["config/packages/cache.yaml", "config/packages/framework.yaml", "config/preload.php", "config/routes/framework.yaml", "config/services.yaml", "public/index.php", "src/Controller/.gitignore", "src/Kernel.php"]}, "symfony/messenger": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.0", "ref": "ba1ac4e919baba5644d31b57a3284d6ba12d52ee"}, "files": ["config/packages/messenger.yaml"]}, "symfony/routing": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.0", "ref": "21b72649d5622d8f7da329ffb5afb232a023619d"}, "files": ["config/packages/routing.yaml", "config/routes.yaml"]}, "symfony/security-bundle": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.4", "ref": "2ae08430db28c8eb4476605894296c82a642028f"}, "files": ["config/packages/security.yaml", "config/routes/security.yaml"]}, "symfony/stimulus-bundle": {"version": "2.25", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.20", "ref": "3acc494b566816514a6873a89023a35440b6386d"}, "files": ["assets/bootstrap.js", "assets/controllers.json", "assets/controllers/csrf_protection_controller.js", "assets/controllers/hello_controller.js"]}, "symfony/translation": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.3", "ref": "e28e27f53663cc34f0be2837aba18e3a1bef8e7b"}, "files": ["config/packages/translation.yaml", "translations/.gitignore"]}, "symfony/twig-bundle": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.4", "ref": "cab5fd2a13a45c266d45a7d9337e28dee6272877"}, "files": ["config/packages/twig.yaml", "templates/base.html.twig"]}, "symfony/validator": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.0", "ref": "8c1c4e28d26a124b0bb273f537ca8ce443472bfd"}, "files": ["config/packages/validator.yaml"]}, "symfony/web-profiler-bundle": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.1", "ref": "8b51135b84f4266e3b4c8a6dc23c9d1e32e543b7"}, "files": ["config/packages/web_profiler.yaml", "config/routes/web_profiler.yaml"]}}