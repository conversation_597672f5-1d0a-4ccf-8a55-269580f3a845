<?php

$finder = PhpCsFixer\Finder::create()
    ->exclude([
        'vendor-bin',
        'assets',
        'migrations',
        'node_modules',
        'var'
    ])
    ->in(__DIR__);


return (new PhpCsFixer\Config())
    ->setRiskyAllowed(true)
    ->setRules([
//        '@PHP74Migration' => true,
//        '@PHP74Migration:risky' => true,
        '@PHP81Migration' => true,
        '@PHP80Migration:risky' => true,
        '@Symfony' => true,
        '@Symfony:risky' => true,
        'combine_consecutive_issets' => true,
        'combine_consecutive_unsets' => true,
        'compact_nullable_type_declaration' => true,
        'global_namespace_import' => true,
        'list_syntax' => ['syntax' => 'short'],
        'mb_str_functions' => true,
        'no_useless_else' => true,
        'no_useless_return' => true,
        'php_unit_strict' => true,
        'phpdoc_order' => true,
        'static_lambda' => true,
        'strict_comparison' => true,
        'strict_param' => true,
        'nullable_type_declaration_for_default_null_value' => true,
    ])
    ->setFinder($finder)
    ->setCacheFile(__DIR__.'/.php-cs-fixer.cache')
;
